'use client';

import React from 'react';

interface FormMessagesProps {
  successMessage?: string | null;
  errorMessage?: string | null;
  validationErrors?: Record<string, string>;
  className?: string;
}

export const FormMessages: React.FC<FormMessagesProps> = ({
  successMessage,
  errorMessage,
  validationErrors = {},
  className = ''
}) => {
  const hasErrors = errorMessage || Object.keys(validationErrors).length > 0;

  return (
    <div className={className}>
      {/* Success Message */}
      {successMessage && (
        <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-check-circle-line text-green-500 mr-2"></i>
            <p className="text-green-700">{successMessage}</p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-500 mr-2"></i>
            <p className="text-red-700">{errorMessage}</p>
          </div>
        </div>
      )}

      {/* Validation Errors */}
      {Object.keys(validationErrors).length > 0 && !errorMessage && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <i className="ri-error-warning-line text-red-500 mr-2 mt-0.5"></i>
            <div>
              <h4 className="text-sm font-medium text-red-900 mb-2">
                Please fix the following issues:
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {Object.entries(validationErrors).map(([field, error]) => (
                  <li key={field}>• {error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

interface SuccessMessageProps {
  message: string;
  onDismiss?: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
  className?: string;
}

export const SuccessMessage: React.FC<SuccessMessageProps> = ({
  message,
  onDismiss,
  autoHide = true,
  autoHideDelay = 5000,
  className = ''
}) => {
  React.useEffect(() => {
    if (autoHide && onDismiss) {
      const timer = setTimeout(() => {
        onDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, onDismiss]);

  return (
    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <i className="ri-check-circle-line text-green-500 mr-2"></i>
          <p className="text-green-700">{message}</p>
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-green-500 hover:text-green-700 ml-4"
            aria-label="Dismiss"
          >
            <i className="ri-close-line"></i>
          </button>
        )}
      </div>
    </div>
  );
};

interface ErrorMessageProps {
  message: string;
  onDismiss?: () => void;
  className?: string;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  onDismiss,
  className = ''
}) => {
  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <i className="ri-error-warning-line text-red-500 mr-2"></i>
          <p className="text-red-700">{message}</p>
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="text-red-500 hover:text-red-700 ml-4"
            aria-label="Dismiss"
          >
            <i className="ri-close-line"></i>
          </button>
        )}
      </div>
    </div>
  );
};

interface ValidationErrorsProps {
  errors: Record<string, string>;
  className?: string;
}

export const ValidationErrors: React.FC<ValidationErrorsProps> = ({
  errors,
  className = ''
}) => {
  if (Object.keys(errors).length === 0) return null;

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <i className="ri-error-warning-line text-red-500 mr-2 mt-0.5"></i>
        <div>
          <h4 className="text-sm font-medium text-red-900 mb-2">
            Please fix the following issues:
          </h4>
          <ul className="text-sm text-red-700 space-y-1">
            {Object.entries(errors).map(([field, error]) => (
              <li key={field}>• {error}</li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default FormMessages;
