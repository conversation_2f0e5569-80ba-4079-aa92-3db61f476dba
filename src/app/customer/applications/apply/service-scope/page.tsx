'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { TextArea } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { scopeOfServiceService, ScopeOfServiceData } from '@/services/scopeOfServiceService';
import { useApplicationData } from '@/hooks/useApplicationData';
import { licenseCategoryService } from '@/services/licenseCategoryService';
import { LicenseType } from '@/services/licenseTypeService';
import router from 'next/router';

const ServiceScopePage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // URL parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'service-scope',
    licenseCategoryId,
    applicationId
  });

  // License data
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);

  // Use application data hook for auto-population
  const {
    saveFormData
  } = useApplicationData({
    applicationId,
    stepName: 'service-scope',
    autoLoad: true
  });

  // Form data state
  const [formData, setFormData] = useState({
    nature_of_service: '',
    premises: '',
    transport_type: '',
    customer_assistance: ''
  });

  // Form handling functions
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);

        console.log('🔄 Loading service scope data for application:', applicationId);

        // Load license category and type data
        if (licenseCategoryId) {
          try {
            const categoryData = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
            if (categoryData && categoryData.license_type) {
              setLicenseType(categoryData.license_type);
            }
          } catch (licenseError) {
            console.warn('Could not load license category data:', licenseError);
          }
        }

        // Load application data
        const application = await applicationService.getApplication(applicationId);
        console.log('📋 Application loaded:', application);

        // Try to load existing scope of service data from form data service
        try {
          console.log('🔍 Loading existing scope of service for application:', applicationId);

          // Load from scope of service API
          const existingScopeOfService = await scopeOfServiceService.getScopeOfServiceByApplication(applicationId);

          if (existingScopeOfService) {
            setFormData({
              nature_of_service: existingScopeOfService.nature_of_service || '',
              premises: existingScopeOfService.premises || '',
              transport_type: existingScopeOfService.transport_type || '',
              customer_assistance: existingScopeOfService.customer_assistance || ''
            });
            console.log('✅ Scope of service data auto-populated from scope of service API');
          } else {
            console.log('📝 No existing scope of service data found');
          }
        } catch (scopeError: any) {
          console.error('❌ Error loading scope of service:', scopeError);
          setLoadingWarning('Could not load existing scope of service data. You can still fill out the form.');
        }

      } catch (err: any) {
        console.error('❌ Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Save function - optimized for performance and proper data persistence
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      if (!formData.nature_of_service.trim()) errors.nature_of_service = 'Nature of service is required';
      if (!formData.premises.trim()) errors.premises = 'Premises information is required';
      if (!formData.transport_type.trim()) errors.transport_type = 'Transport type is required';
      if (!formData.customer_assistance.trim()) errors.customer_assistance = 'Customer assistance information is required';

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSaving(false);
        return false;
      }

      console.log('💾 Saving service scope information for application:', applicationId);

      // Create or update scope of service record using the proper API
      const scopeOfServiceData = {
        nature_of_service: formData.nature_of_service,
        premises: formData.premises,
        transport_type: formData.transport_type,
        customer_assistance: formData.customer_assistance
      };

      // Execute save operations in parallel for better performance
      const savePromises = [
        // Save to scope of service API
        scopeOfServiceService.createOrUpdateScopeOfService(applicationId, scopeOfServiceData),
        // Update application progress
        applicationService.updateApplication(applicationId, {
          current_step: 6,
          progress_percentage: 86 // ~6/7 steps completed
        })
      ];

      try {
        await Promise.all(savePromises);
        console.log('✅ Service scope data and progress saved successfully');
      } catch (saveError: any) {
        console.error('❌ Error saving service scope data:', saveError);
        throw new Error('Failed to save service scope information');
      }

      setHasUnsavedChanges(false);
      setValidationErrors({});
      setSuccessMessage('Service scope information saved successfully!');

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('✅ Service scope information saved successfully');
      return true;

    } catch (error: any) {
      console.error('❌ Error saving service scope information:', error);
      setValidationErrors({ save: 'Failed to save service scope information. Please try again.' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading service scope form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Form</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => router.back()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Service Scope Information"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {applicationId ? 'Edit Service Scope Information' : 'Service Scope Information'}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {applicationId
              ? 'Update your service scope and operational details below.'
              : 'Provide details about the scope of services you plan to offer.'
            }
          </p>
          {applicationId && !loadingWarning && !isLoading && (
            <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                ✅ Editing existing application. Your saved service scope information has been loaded.
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
        </div>

        {/* Form Messages */}
        <FormMessages
          successMessage={successMessage}
          errorMessage={validationErrors.save}
          validationErrors={Object.fromEntries(
            Object.entries(validationErrors).filter(([key]) => key !== 'save')
          )}
        />

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="space-y-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Service Scope Details
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Define the scope of services you plan to offer and your operational details.
              </p>
            </div>

            <div className="space-y-6">
              <TextArea
                label="Nature of Service"
                value={formData.nature_of_service}
                onChange={(e) => handleFormChange('nature_of_service', e.target.value)}
                error={validationErrors.nature_of_service}
                rows={4}
                placeholder="Describe the nature and type of services you plan to offer..."
                required
              />

              <TextArea
                label="Premises"
                value={formData.premises}
                onChange={(e) => handleFormChange('premises', e.target.value)}
                error={validationErrors.premises}
                rows={3}
                placeholder="Describe your business premises and facilities..."
                required
              />

              <TextArea
                label="Transport Type"
                value={formData.transport_type}
                onChange={(e) => handleFormChange('transport_type', e.target.value)}
                error={validationErrors.transport_type}
                rows={3}
                placeholder="Describe the types of transport and logistics you will use..."
                required
              />

              <TextArea
                label="Customer Assistance"
                value={formData.customer_assistance}
                onChange={(e) => handleFormChange('customer_assistance', e.target.value)}
                error={validationErrors.customer_assistance}
                rows={3}
                placeholder="Describe how you will provide customer assistance and support..."
                required
              />
            </div>
          </div>
        </div>

      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default ServiceScopePage;
