'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import ApplicationLayout from '@/components/applications/ApplicationLayout';
import { useAuth } from '@/contexts/AuthContext';
import { CustomerApiService } from '@/lib/customer-api';
import { TextInput, TextArea, Select } from '@/components/forms';
import { FormMessages } from '@/components/forms/FormMessages';
import { useDynamicNavigation } from '@/hooks/useDynamicNavigation';
import { applicationService } from '@/services/applicationService';
import { stakeholderService } from '@/services/stakeholderService';
import { useApplicationData } from '@/hooks/useApplicationData';
import { applicationProgressService } from '@/services/applicationProgressService';
import { LicenseType } from '@/services/licenseTypeService';

import { getLicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';

// Import stakeholder interface from service
import { StakeholderData, CreateStakeholderData } from '@/services/stakeholderService';
import { licenseCategoryService } from '@/services/licenseCategoryService';

const ManagementPage: React.FC = () => {
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [loadingWarning, setLoadingWarning] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Dynamic navigation hook
  const {
    handleNext: dynamicHandleNext,
    handlePrevious: dynamicHandlePrevious,
    nextStep
  } = useDynamicNavigation({
    currentStepRoute: 'management',
    licenseCategoryId,
    applicationId
  });

  // License data
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);

  // Use application data hook for auto-population
  const {
    saveFormData
  } = useApplicationData({
    applicationId,
    stepName: 'management',
    autoLoad: true
  });

  // Form data state
  const [formData, setFormData] = useState({
    stakeholders: [] as StakeholderData[],
    organizational_structure: '',
    key_personnel: '',
    management_experience: '',
    leadership_approach: '',
    succession_planning: ''
  });

  // Form handling functions
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear success message when user starts making changes
    if (successMessage) {
      setSuccessMessage(null);
    }

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Stakeholder management functions
  const addStakeholder = useCallback(() => {
    const newStakeholder: StakeholderData = {
      first_name: '',
      last_name: '',
      middle_name: '',
      nationality: '',
      position: 'CEO',
      profile: ''
    };
    setFormData(prev => ({
      ...prev,
      stakeholders: [...prev.stakeholders, newStakeholder]
    }));
    setHasUnsavedChanges(true);
  }, []);

  const updateStakeholder = (index: number, field: keyof StakeholderData, value: string) => {
    const updatedStakeholders = formData.stakeholders.map((stakeholder, i) =>
      i === index ? { ...stakeholder, [field]: value } : stakeholder
    );
    handleFormChange('stakeholders', updatedStakeholders);
  };

  const removeStakeholder = (index: number) => {
    const updatedStakeholders = formData.stakeholders.filter((_, i) => i !== index);
    handleFormChange('stakeholders', updatedStakeholders);
  };



  // Load existing data
  useEffect(() => {
    const loadData = async () => {
      if (!applicationId || !isAuthenticated || authLoading) return;

      try {
        setIsLoading(true);
        setError(null);
        setLoadingWarning(null);

        console.log('🔄 Loading management data for application:', applicationId);

        // Load license category and type data
        if (licenseCategoryId) {
          try {
            const categoryData = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
            if (categoryData && categoryData.license_type) {
              setLicenseType(categoryData.license_type);
            }
          } catch (licenseError) {
            console.warn('Could not load license category data:', licenseError);
          }
        }

        // Load application and applicant data
        const application = await applicationService.getApplication(applicationId);
        console.log('📋 Application loaded:', application);

        if (application.applicant_id) {
          try {
            console.log('👥 Loading existing stakeholders for applicant:', application.applicant_id);
            const existingStakeholders = await stakeholderService.getStakeholdersByApplicant(application.applicant_id);
            console.log('👥 Found existing stakeholders:', existingStakeholders);

            if (existingStakeholders.length > 0) {
              // Auto-populate with existing stakeholder data
              setFormData(prev => ({
                ...prev,
                stakeholders: existingStakeholders.map(stakeholder => ({
                  stakeholder_id: stakeholder.stakeholder_id,
                  first_name: stakeholder.first_name || '',
                  last_name: stakeholder.last_name || '',
                  middle_name: stakeholder.middle_name || '',
                  nationality: stakeholder.nationality || '',
                  position: stakeholder.position || 'CEO',
                  profile: stakeholder.profile || '',
                  contact_id: stakeholder.contact_id,
                  cv_document_id: stakeholder.cv_document_id
                }))
              }));
              console.log('✅ Stakeholder data auto-populated');
            } else {
              // No existing stakeholders, initialize with one empty stakeholder
              setFormData(prev => ({
                ...prev,
                stakeholders: [{
                  first_name: '',
                  last_name: '',
                  middle_name: '',
                  nationality: '',
                  position: 'CEO',
                  profile: ''
                }]
              }));
              console.log('📝 Added empty stakeholder for new data');
            }
          } catch (stakeholderError: any) {
            console.error('❌ Error loading stakeholders:', stakeholderError);
            setLoadingWarning('Could not load existing stakeholder data. You can still add stakeholders, but the form will start empty.');
            // Add empty stakeholder as fallback
            setFormData(prev => ({
              ...prev,
              stakeholders: prev.stakeholders.length === 0 ? [{
                first_name: '',
                last_name: '',
                middle_name: '',
                nationality: '',
                position: 'CEO',
                profile: ''
              }] : prev.stakeholders
            }));
          }
        } else {
          console.log('⚠️ Application exists but no applicant_id found');
          setFormData(prev => ({
            ...prev,
            stakeholders: prev.stakeholders.length === 0 ? [{
              first_name: '',
              last_name: '',
              middle_name: '',
              nationality: '',
              position: 'CEO',
              profile: ''
            }] : prev.stakeholders
          }));
        }

      } catch (err: any) {
        console.error('❌ Error loading application data:', err);
        setError('Failed to load application data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [applicationId, isAuthenticated, authLoading]);

  // Save function - following contact-info pattern
  const handleSave = async (): Promise<boolean> => {
    if (!applicationId) {
      setValidationErrors({ save: 'Application ID is required' });
      return false;
    }

    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      if (formData.stakeholders.length === 0) {
        errors.stakeholders = 'At least one stakeholder is required';
      } else {
        formData.stakeholders.forEach((stakeholder, index) => {
          if (!stakeholder.first_name.trim()) errors[`stakeholder_${index}_first_name`] = 'First name is required';
          if (!stakeholder.last_name.trim()) errors[`stakeholder_${index}_last_name`] = 'Last name is required';
          if (!stakeholder.nationality.trim()) errors[`stakeholder_${index}_nationality`] = 'Nationality is required';
          if (!stakeholder.profile.trim()) errors[`stakeholder_${index}_profile`] = 'Profile is required';
        });
      }

      if (!formData.organizational_structure.trim()) errors.organizational_structure = 'Organizational structure is required';

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSaving(false);
        return false;
      }

      console.log('💾 Saving management information for application:', applicationId);

      // Get application and applicant
      const application = await applicationService.getApplication(applicationId);
      if (!application.applicant_id) {
        throw new Error('No applicant found for this application');
      }

      console.log('👤 Found applicant ID:', application.applicant_id);

      // Get existing stakeholders for this applicant
      let existingStakeholders: StakeholderData[] = [];
      try {
        existingStakeholders = await stakeholderService.getStakeholdersByApplicant(application.applicant_id);
      } catch (err) {
        console.log('No existing stakeholders found, will create new ones');
        existingStakeholders = [];
      }

      // Create or update stakeholder records using the proper API
      const stakeholderPromises = [];

      // Save each stakeholder
      for (let i = 0; i < formData.stakeholders.length; i++) {
        const stakeholder = formData.stakeholders[i];
        const stakeholderData: CreateStakeholderData = {
          applicant_id: application.applicant_id,
          first_name: stakeholder.first_name,
          last_name: stakeholder.last_name,
          middle_name: stakeholder.middle_name,
          nationality: stakeholder.nationality,
          position: stakeholder.position,
          profile: stakeholder.profile
        };

        // Check if this stakeholder already exists (by index or stakeholder_id)
        const existingStakeholder = existingStakeholders[i] ||
          (stakeholder.stakeholder_id ? existingStakeholders.find(s => s.stakeholder_id === stakeholder.stakeholder_id) : null);

        if (existingStakeholder && existingStakeholder.stakeholder_id) {
          // Update existing stakeholder
          console.log('📝 Updating existing stakeholder:', existingStakeholder.stakeholder_id);
          stakeholderPromises.push(
            stakeholderService.updateStakeholder({
              stakeholder_id: existingStakeholder.stakeholder_id,
              ...stakeholderData
            })
          );
        } else {
          // Create new stakeholder
          console.log('➕ Creating new stakeholder');
          stakeholderPromises.push(
            stakeholderService.createStakeholder(stakeholderData)
          );
        }
      }

      // Execute save operations in parallel for better performance
      const savePromises = [
        // Save all stakeholders
        ...stakeholderPromises,
        // Update application progress
        applicationService.updateApplication(applicationId, {
          current_step: 5,
          progress_percentage: 71 // ~5/7 steps completed
        })
      ];

      try {
        await Promise.all(savePromises);
        console.log('✅ Management data and progress saved successfully');
      } catch (saveError: any) {
        console.error('❌ Error saving management data:', saveError);
        throw new Error('Failed to save management information');
      }

      setHasUnsavedChanges(false);
      setValidationErrors({});
      setSuccessMessage('Management information saved successfully!');

      // Auto-hide success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      console.log('✅ Management information saved successfully');
      return true;

    } catch (error: any) {
      console.error('❌ Error saving management information:', error);
      setValidationErrors({ save: 'Failed to save management information. Please try again.' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };



  // Navigation functions using dynamic navigation
  const handleNext = async () => {
    await dynamicHandleNext(handleSave);
  };

  const handlePrevious = () => {
    dynamicHandlePrevious();
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading management form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Form</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => dynamicHandlePrevious()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <ApplicationLayout
        onNext={handleNext}
        onPrevious={handlePrevious}
        onSave={handleSave}
        showNextButton={true}
        showPreviousButton={true}
        showSaveButton={true}
        nextButtonText={nextStep ? `Continue to ${nextStep.name}` : "Continue"}
        previousButtonText="Back to Previous Step"
        saveButtonText="Save Management Information"
        nextButtonDisabled={false}
        isSaving={isSaving}
      >
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {applicationId ? 'Edit Management Information' : 'Management Information'}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {applicationId
              ? 'Update your management team and organizational information below.'
              : 'Provide details about your organization\'s management team and structure.'
            }
          </p>
          {applicationId && !loadingWarning && !isLoading && (
            <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <p className="text-sm text-green-700 dark:text-green-300">
                ✅ Editing existing application. Your saved management information has been loaded.
              </p>
            </div>
          )}
          {loadingWarning && (
            <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                ⚠️ {loadingWarning}
              </p>
            </div>
          )}
        </div>

        {/* Form Messages */}
        <FormMessages
          successMessage={successMessage}
          errorMessage={validationErrors.save}
          validationErrors={Object.fromEntries(
            Object.entries(validationErrors).filter(([key]) => key !== 'save')
          )}
        />

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          {/* Stakeholders Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Key Stakeholders
              </h3>
              <button
                type="button"
                onClick={addStakeholder}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-add-line mr-1"></i>
                Add Stakeholder
              </button>
            </div>

            {formData.stakeholders.length === 0 && (
              <div className="text-center py-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                <p className="text-gray-500 dark:text-gray-400">No stakeholders added yet.</p>
                <button
                  type="button"
                  onClick={addStakeholder}
                  className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  <i className="ri-add-line mr-1"></i>
                  Add First Stakeholder
                </button>
              </div>
            )}

            {formData.stakeholders.map((stakeholder, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">
                    Stakeholder {index + 1}
                  </h4>
                  <button
                    type="button"
                    onClick={() => removeStakeholder(index)}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  >
                    <i className="ri-delete-bin-line"></i>
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    label="First Name"
                    value={stakeholder.first_name}
                    onChange={(e) => updateStakeholder(index, 'first_name', e.target.value)}
                    error={validationErrors[`stakeholder_${index}_first_name`]}
                    required
                  />
                  <TextInput
                    label="Last Name"
                    value={stakeholder.last_name}
                    onChange={(e) => updateStakeholder(index, 'last_name', e.target.value)}
                    error={validationErrors[`stakeholder_${index}_last_name`]}
                    required
                  />
                  <TextInput
                    label="Middle Name"
                    value={stakeholder.middle_name || ''}
                    onChange={(e) => updateStakeholder(index, 'middle_name', e.target.value)}
                  />
                  <TextInput
                    label="Nationality"
                    value={stakeholder.nationality}
                    onChange={(e) => updateStakeholder(index, 'nationality', e.target.value)}
                    error={validationErrors[`stakeholder_${index}_nationality`]}
                    required
                  />
                  <Select
                    label="Position"
                    value={stakeholder.position}
                    onChange={(e) => updateStakeholder(index, 'position', e.target.value)}
                    options={[
                      { value: 'CEO', label: 'Chief Executive Officer (CEO)' },
                      { value: 'SHAREHOLDER', label: 'Shareholder' },
                      { value: 'AUDITOR', label: 'Auditor' },
                      { value: 'LAWYER', label: 'Lawyer' }
                    ]}
                    required
                  />
                </div>

                <div className="mt-4">
                  <TextArea
                    label="Profile/Background"
                    value={stakeholder.profile}
                    onChange={(e) => updateStakeholder(index, 'profile', e.target.value)}
                    error={validationErrors[`stakeholder_${index}_profile`]}
                    rows={3}
                    placeholder="Describe the professional background and qualifications of this stakeholder..."
                    required
                  />
                </div>
              </div>
            ))}
          </div>

    
        </div>

      </ApplicationLayout>
    </CustomerLayout>
  );
};

export default ManagementPage;
