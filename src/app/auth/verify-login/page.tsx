'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import {
  EnvelopeIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/solid';
import Loader from '@/components/Loader';

export default function VerifyLoginPage() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(30);
  const [loading, setLoading] = useState(true);

  // Countdown timer for redirect
  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          router.push('/auth/login');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  const handleBackToLogin = () => {
    setTimeout(() => {
      setLoading(false);
    }, 300);
    router.push('/auth/login');
  };

  return (
    loading ? (
      <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <Loader message={(countdown > 1) ? "Waiting for verification" : "Redirecting to login..."} />
        </div>
      </div>
    ) : (
    <div className="min-h-screen flex flex-col justify-center py-12 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
      <div className="sm:mx-auto sm:w-full sm:max-w-md text-center">
        <Image
          src="/images/macra-logo.png"
          alt="MACRA Logo"
          width={50}
          height={50}
          className="mx-auto h-16 w-auto"
        />
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          Login Verification Required
        </h2>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="flex flex-col items-center justify-center text-center">
            {/* Email Icon */}
            <div className="w-16 h-16 mb-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center shadow-md">
              <EnvelopeIcon className="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>

            {/* Main Message */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                OTP Link Sent to Email
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                We&apos;ve sent a one-time password link to your registered email address.
                Please check your email and click the link to complete your login.
              </p>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6 w-full">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    What to do next:
                  </h4>
                  <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                    <ul className="list-disc list-inside space-y-1">
                      <li>Check your email inbox (and spam folder)</li>
                      <li>Look for an email from MACRA</li>
                      <li>Click the login link in the email</li>
                      <li>You will be automatically logged in</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Auto redirect notice */}
            <div className="text-center text-sm text-gray-500 dark:text-gray-400 mb-6">
              <ArrowPathIcon className="w-4 h-4 inline mr-1" />
              Redirecting to login in {countdown} seconds
            </div>

            {/* Back to Login Button */}
            <button
              onClick={handleBackToLogin}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Login
            </button>
          </div>
        </div>
      </div>
    </div>
  ));
}